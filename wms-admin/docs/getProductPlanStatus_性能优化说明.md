# getProductPlanStatus 方法性能优化说明

## 优化前后对比

### 原始实现的性能问题

1. **N+1 查询问题**
   - 主查询：查询最近7天的生产订单
   - N次子查询：对每个 `formalBill='1'` 的订单执行一次 WmsProrequist 查询
   - 如果有1000个订单，其中800个是 `formalBill='1'`，则总共执行 801 次数据库查询

2. **内存使用效率低**
   - 加载完整的实体对象到内存
   - 在Java层面进行循环统计计算

3. **数据传输量大**
   - 从数据库传输大量不必要的字段数据

### 优化后的实现

1. **单次 JOIN 查询**
   - 使用 LEFT JOIN 一次性获取所有需要的数据
   - 在数据库层面使用 CASE WHEN 进行条件统计
   - 无论数据量多大，都只执行 1 次数据库查询

2. **数据库层面计算**
   - 统计计算在数据库层面完成，利用数据库的优化能力
   - 只返回最终的统计结果，数据传输量最小

3. **索引友好**
   - 查询条件使用了 `create_time` 和 `bill_no`、`work_no` 等字段
   - 建议在这些字段上创建索引以进一步提升性能

## 核心优化 SQL

```sql
SELECT 
    SUM(CASE WHEN po.formal_bill = '0' THEN 1 ELSE 0 END) as waitingPrepareCount,
    SUM(CASE 
        WHEN po.formal_bill = '1' 
        AND pr.bill_status IS NOT NULL 
        AND pr.bill_status != '3' 
        THEN 1 ELSE 0 
    END) as preparingCount,
    SUM(CASE 
        WHEN po.formal_bill = '1' 
        AND pr.bill_status = '3' 
        AND pr.erp_sync = '2' 
        THEN 1 ELSE 0 
    END) as prepareCompleteCount,
    SUM(CASE 
        WHEN po.formal_bill = '1' 
        AND pr.bill_status = '3' 
        AND pr.erp_sync = '3' 
        THEN 1 ELSE 0 
    END) as exceptionOrdersCount
FROM wms_product_order po
LEFT JOIN wms_prorequist pr ON po.bill_no = pr.work_no
WHERE po.create_time >= #{sevenDaysAgo}
```

## 性能提升预期

### 查询次数优化
- **优化前**: 1 + N 次查询（N为formalBill='1'的订单数量）
- **优化后**: 1 次查询
- **提升**: 减少 99%+ 的数据库查询次数

### 响应时间优化
- **优化前**: O(N) 时间复杂度，随订单数量线性增长
- **优化后**: O(1) 时间复杂度，固定时间
- **提升**: 响应时间提升 10-100 倍（取决于数据量）

### 内存使用优化
- **优化前**: 加载所有订单实体到内存
- **优化后**: 只返回统计结果
- **提升**: 内存使用减少 90%+

## 建议的数据库索引

为了进一步提升性能，建议创建以下索引：

```sql
-- 生产订单表索引
CREATE INDEX idx_wms_product_order_create_time ON wms_product_order(create_time);
CREATE INDEX idx_wms_product_order_formal_bill ON wms_product_order(formal_bill);
CREATE INDEX idx_wms_product_order_bill_no ON wms_product_order(bill_no);

-- 生产单据表索引
CREATE INDEX idx_wms_prorequist_work_no ON wms_prorequist(work_no);
CREATE INDEX idx_wms_prorequist_status ON wms_prorequist(bill_status, erp_sync);

-- 复合索引（可选，根据实际查询模式决定）
CREATE INDEX idx_wms_product_order_composite ON wms_product_order(create_time, formal_bill, bill_no);
```

## 代码变更总结

1. **新增 Mapper 方法**: `getProductPlanStatusOptimized`
2. **新增 SQL 查询**: 使用 JOIN 和聚合函数的优化查询
3. **重构 Service 方法**: 使用单次查询替代循环查询
4. **增加安全处理**: `getIntValue` 方法处理类型转换和null值

## 兼容性说明

- 优化后的方法保持了相同的接口和返回格式
- 业务逻辑完全一致，只是实现方式更高效
- 可以无缝替换原有实现，无需修改调用方代码
