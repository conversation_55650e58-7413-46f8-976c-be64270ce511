package org.jeecg.modules.admin.service;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.modules.admin.service.impl.WmsProductOrderServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * WmsProductOrderService 测试类
 */
@SpringBootTest
@SpringJUnitConfig
public class WmsProductOrderServiceTest {

    @Autowired
    private IWmsProductOrderService wmsProductOrderService;

    @Test
    public void testGetProductPlanStatus() {
        try {
            JSONObject result = wmsProductOrderService.getProductPlanStatus();
            
            System.out.println("生产订单统计结果:");
            System.out.println("待备料订单数: " + result.get("waitingPrepareCount"));
            System.out.println("备料中订单数: " + result.get("preparingCount"));
            System.out.println("备料完成订单数: " + result.get("prepareCompleteCount"));
            System.out.println("异常订单数: " + result.get("exceptionOrdersCount"));
            System.out.println("完成率: " + result.get("completionRate") + "%");
            
            // 验证返回结果包含所有必要字段
            assert result.containsKey("waitingPrepareCount");
            assert result.containsKey("preparingCount");
            assert result.containsKey("prepareCompleteCount");
            assert result.containsKey("exceptionOrdersCount");
            assert result.containsKey("completionRate");
            
            // 验证数值类型
            assert result.get("waitingPrepareCount") instanceof Integer;
            assert result.get("preparingCount") instanceof Integer;
            assert result.get("prepareCompleteCount") instanceof Integer;
            assert result.get("exceptionOrdersCount") instanceof Integer;
            assert result.get("completionRate") instanceof Double;
            
            System.out.println("测试通过！");
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
