<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.admin.mapper.WmsProductOrderMapper">

    <!-- 优化的生产订单统计查询 -->
    <select id="getProductPlanStatusOptimized" resultType="java.util.HashMap">
        SELECT
            SUM(CASE WHEN po.formal_bill = '0' THEN 1 ELSE 0 END) as waitingPrepareCount,
            SUM(CASE
                WHEN po.formal_bill = '1'
                AND pr.bill_status IS NOT NULL
                AND pr.bill_status != '3'
                THEN 1 ELSE 0
            END) as preparingCount,
            SUM(CASE
                WHEN po.formal_bill = '1'
                AND pr.bill_status = '3'
                AND pr.erp_sync = '2'
                THEN 1 ELSE 0
            END) as prepareCompleteCount,
            SUM(CASE
                WHEN po.formal_bill = '1'
                AND pr.bill_status = '3'
                AND pr.erp_sync = '3'
                THEN 1 ELSE 0
            END) as exceptionOrdersCount
        FROM wms_product_order po
        LEFT JOIN wms_prorequist pr ON po.bill_no = pr.work_no
        WHERE po.create_time >= #{sevenDaysAgo}
    </select>

</mapper>