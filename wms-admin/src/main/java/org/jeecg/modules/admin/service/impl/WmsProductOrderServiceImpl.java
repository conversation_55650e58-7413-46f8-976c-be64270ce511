package org.jeecg.modules.admin.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jeecg.common.api.dto.LogDTO;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.constant.FillRuleConstant;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.FillRuleUtil;
import org.jeecg.modules.admin.entity.*;
import org.jeecg.modules.admin.mapper.WmsProductOrderMapper;
import org.jeecg.modules.admin.mapper.WmsUseMaterialdetailMapper;
import org.jeecg.modules.admin.service.*;
import org.jeecg.modules.admin.util.HttpClientUtil;
import org.jeecg.modules.base.service.BaseCommonService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.Serializable;
import java.net.InetAddress;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

/**
 * 生产订单同步示例（增量同步 + 边请求边写入 + 同步用料清单明细）
 */
@Service
public class WmsProductOrderServiceImpl extends ServiceImpl<WmsProductOrderMapper, WmsProductOrder> implements IWmsProductOrderService {

    private static final Logger log = LoggerFactory.getLogger(WmsProductOrderServiceImpl.class);

    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private BaseCommonService baseCommonService;
    @Autowired
    private IMesInterfaceService mesInterfaceService;
    @Autowired
    private WmsProductOrderMapper wmsProductOrderMapper;
    @Autowired
    private WmsUseMaterialdetailMapper wmsUseMaterialdetailMapper;
    @Autowired
    private IWmsProrequistService wmsProrequistService;
    @Autowired
    private IWmsProrequistDetailService wmsProrequistDetailService;
    @Autowired
    private IWmsSpecMatchItemService wmsSpecMatchItemService;
    @Autowired
    private IWmsFeatureConfigurationService wmsFeatureConfigurationService;

    // 每页大小，根据实际情况调优
    private static final int PAGE_SIZE = 5000;
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");

    /**
     * 保存主表和子表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMain(WmsProductOrder wmsProductOrder, List<WmsUseMaterialdetail> wmsUseMaterialdetailList) {
        wmsProductOrderMapper.insert(wmsProductOrder);
        if (wmsUseMaterialdetailList != null && !wmsUseMaterialdetailList.isEmpty()) {
            for (WmsUseMaterialdetail entity : wmsUseMaterialdetailList) {
                //外键设置
                entity.setBillId(wmsProductOrder.getId());
                wmsUseMaterialdetailMapper.insert(entity);
            }
        }
    }

    /**
     * 更新主表和子表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMain(WmsProductOrder wmsProductOrder, List<WmsUseMaterialdetail> wmsUseMaterialdetailList) {
        wmsProductOrderMapper.updateById(wmsProductOrder);

        //1.先删除子表数据
        wmsUseMaterialdetailMapper.deleteByMainId(wmsProductOrder.getId());

        //2.子表数据重新插入
        if (wmsUseMaterialdetailList != null && !wmsUseMaterialdetailList.isEmpty()) {
            for (WmsUseMaterialdetail entity : wmsUseMaterialdetailList) {
                //外键设置
                entity.setBillId(wmsProductOrder.getId());
                wmsUseMaterialdetailMapper.insert(entity);
            }
        }
    }

    /**
     * 删除主表和子表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delMain(String id) {
        wmsUseMaterialdetailMapper.deleteByMainId(id);
        wmsProductOrderMapper.deleteById(id);
    }

    /**
     * 批量删除主表和子表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delBatchMain(Collection<? extends Serializable> idList) {
        for (Serializable id : idList) {
            wmsUseMaterialdetailMapper.deleteByMainId(id.toString());
            wmsProductOrderMapper.deleteById(id);
        }
    }

    /**
     * 同步生产订单数据，并在完成后同步用料清单明细
     */
    @Override
    public void syncProductOrderData() {
        // 1. 先同步生产订单
        String accessToken = redisTemplate.opsForValue().get("mes_login_access_token");
        if (accessToken == null) {
            // 如果没有获取到MES登录token，则调用performLoginAndStoreToken方法获取
            try {
                log.info("未获取到MES登录token，正在调用登录接口获取...");
                mesInterfaceService.performLoginAndStoreToken();
                // 重新获取token
                accessToken = redisTemplate.opsForValue().get("mes_login_access_token");
                if (accessToken == null) {
                    throw new RuntimeException("登录接口调用成功但仍未获取到MES登录token！");
                }
                log.info("已成功获取MES登录token");
            } catch (IOException e) {
                throw new RuntimeException("调用MES登录接口时发生异常: " + e.getMessage(), e);
            }
        }
        // 获取生产订单接口配置信息 JK013
        MesInterface orderInterface = getMesInterfaceByCode("JK013");

        String redisKeyPrefix = orderInterface.getRedisKey();
        if (redisKeyPrefix == null || redisKeyPrefix.trim().isEmpty()) {
            redisKeyPrefix = "mes_product_order";
        }
        String lastSyncDateKey = redisKeyPrefix + "_time";
        String lastSyncDateStr = redisTemplate.opsForValue().get(lastSyncDateKey);
        
        // 获取当前日期，用于更新Redis和作为查询结束时间
        Calendar calendar = Calendar.getInstance();
        String todayStr = DATE_FORMAT.format(calendar.getTime());
        
        // 从功能配置中获取时间范围设置
        int daysToAdd = 1; // 默认为1天
        try {
            // 查询配置表中的productionOrderTimeRange配置
            QueryWrapper<WmsFeatureConfiguration> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("field", "productionOrderTimeRange");
            List<WmsFeatureConfiguration> configs = wmsFeatureConfigurationService.list(queryWrapper);
            
            if (configs != null && !configs.isEmpty() && configs.get(0).getValue() != null) {
                String configValue = configs.get(0).getValue();
                // 配置值可能是"1,2"这样的格式，取第一个值
                if (configValue.contains(",")) {
                    configValue = configValue.split(",")[0];
                }
                daysToAdd = Integer.parseInt(configValue);
                log.info("从配置获取到生产订单时间范围: {} 天", daysToAdd);
            }
        } catch (Exception e) {
            log.error("获取生产订单时间范围配置失败，使用默认值1天: {}", e.getMessage());
        }
        
        // 根据配置计算结束日期
        Calendar endCalendar = Calendar.getInstance();
        endCalendar.add(Calendar.DAY_OF_MONTH, daysToAdd);
        String endDateStr = DATE_FORMAT.format(endCalendar.getTime());
        log.info("生产订单查询时间范围: {} 至 {}", lastSyncDateStr, endDateStr);

        // 构造请求参数，先pageNo=1,pageSize=1只为获取 total
        JSONObject requestParams = new JSONObject();
        requestParams.put("pageNo", 1);
        requestParams.put("pageSize", 1);

        // 如果有上次同步日期，则使用时间区间
        if (lastSyncDateStr != null && !lastSyncDateStr.trim().isEmpty()) {
            requestParams.put("fdate", new JSONArray(Arrays.asList(lastSyncDateStr, endDateStr)));
        }

        // 调用接口，获取 total
        JSONObject firstPageResult = doPost(orderInterface.getInterfaceUrl(), accessToken, requestParams);
        int total = firstPageResult.getInteger("total");
        if (total == 0) {
            log.info("指定时间区间未查询到生产订单数据");
            // 更新最后同步时间为当天日期
            redisTemplate.opsForValue().set(lastSyncDateKey, todayStr);
            return;
        }

        int totalPages = (int) Math.ceil((double) total / PAGE_SIZE);
        log.info("生产订单共{}条记录，需同步{}页", total, totalPages);

        // 查询本地已有的生产订单，建立billNo -> 实体的映射
        List<WmsProductOrder> existingList = this.list();
        Map<String, WmsProductOrder> existingMap = existingList.stream()
                .filter(o -> o.getBillNo() != null)
                .collect(Collectors.toMap(WmsProductOrder::getBillNo, Function.identity(), (o1, o2) -> o1));

        // 用于统计总插入、更新数
        int insertCountTotal = 0;
        int updateCountTotal = 0;

        // === 新增：收集本次处理过的订单号与主键ID映射，用于后续同步用料清单明细
        List<SyncOrderRecord> allProcessedOrders = new ArrayList<>();

        // 2. 分页处理生产订单
        for (int pageNum = 1; pageNum <= totalPages; pageNum++) {
            requestParams.put("pageNo", pageNum);
            requestParams.put("pageSize", PAGE_SIZE);
            log.info("请求参数：{}", requestParams.toJSONString());

            JSONObject pageResult = doPost(orderInterface.getInterfaceUrl(), accessToken, requestParams);
            log.info("响应结果：{}", pageResult.toJSONString());
            JSONArray rows = pageResult.getJSONArray("rows");
            if (rows == null || rows.isEmpty()) {
                continue;
            }

            // 分页插入/更新
            PageResult result = processOnePage(rows, existingMap);
            insertCountTotal += result.insertCount;
            updateCountTotal += result.updateCount;

            // 收集本页处理的订单
            if (result.getProcessedOrders() != null) {
                allProcessedOrders.addAll(result.getProcessedOrders());
            }

            log.info("已处理第{}/{}页，新增{}条，更新{}条", pageNum, totalPages,
                    result.insertCount, result.updateCount);
        }

        log.info("生产订单同步完成，总共新增{}条，更新{}条", insertCountTotal, updateCountTotal);
        // 更新最后同步时间为当天日期，而不是明天
        redisTemplate.opsForValue().set(lastSyncDateKey, todayStr);

        // 3. 生产订单同步完成后，再去同步用料清单明细（JK020）
//        syncMaterialDetailsAfterOrders(allProcessedOrders);
    }

    /**
     * 分页处理一页的生产订单数据，结束时提交事务
     */
    @Transactional(rollbackFor = Exception.class)
    public PageResult processOnePage(JSONArray rows, Map<String, WmsProductOrder> existingMap) {
        Date now = new Date();
        List<WmsProductOrder> insertList = new ArrayList<>(PAGE_SIZE);
        List<WmsProductOrder> updateList = new ArrayList<>(PAGE_SIZE);
        // 用于收集本页处理的 (orderId, billNo)
        List<SyncOrderRecord> processedOrders = new ArrayList<>(PAGE_SIZE);

        for (int i = 0; i < rows.size(); i++) {
            JSONObject obj = rows.getJSONObject(i);
            String fbillno = obj.getString("fbillno");
            if (fbillno == null || fbillno.trim().isEmpty()) {
                continue;
            }

            // 判断是否在本地已存在
            WmsProductOrder order = existingMap.get(fbillno);
            boolean isNew = (order == null);
            if (isNew) {
                order = new WmsProductOrder();
                order.setId(UUID.randomUUID().toString().replace("-", ""));
                order.setCreateTime(now);
            }

            // 将JSON字段映射给本地实体
            mapToProductOrder(order, obj, now);

            if (isNew) {
                insertList.add(order);
                existingMap.put(fbillno, order);
            } else {
                updateList.add(order);
            }
        }

        int insertCount = 0;
        int updateCount = 0;

        // 写入数据库
        if (!insertList.isEmpty()) {
            this.saveBatch(insertList);
            insertCount = insertList.size();
        }
        if (!updateList.isEmpty()) {
            this.updateBatchById(updateList);
            updateCount = updateList.size();
        }

        // 收集本页实际处理的订单 (插入+更新)
        for (WmsProductOrder inserted : insertList) {
            processedOrders.add(new SyncOrderRecord(inserted.getId(), inserted.getBillNo()));
        }
        for (WmsProductOrder updated : updateList) {
            processedOrders.add(new SyncOrderRecord(updated.getId(), updated.getBillNo()));
        }

        PageResult result = new PageResult(insertCount, updateCount);
        result.setProcessedOrders(processedOrders);

        // 分页处理完毕，方法结束时提交事务
        return result;
    }

    /**
     * 根据 code 查询接口（要求请求方式=POST），用于 JK013
     */
    private MesInterface getMesInterfaceByCode(String code) {
        MesInterface mesInterface = mesInterfaceService.getOne(
                new QueryWrapper<MesInterface>().eq("interface_code", code)
        );
        if (mesInterface == null) {
            throw new RuntimeException("未找到接口编号为 " + code + " 的MES接口配置信息！");
        }
        if (!"1".equals(mesInterface.getInterfaceStatus())) {
            throw new RuntimeException("接口 " + code + " 未启用！");
        }
        if (!"POST".equalsIgnoreCase(mesInterface.getRequestMethod())) {
            throw new RuntimeException("接口 " + code + " 的请求方式不是POST！");
        }
        return mesInterface;
    }

    /**
     * 根据 code 查询接口（要求请求方式=GET），用于 JK020
     */
    private MesInterface getMesInterfaceByCodeForGET(String code) {
        MesInterface mesInterface = mesInterfaceService.getOne(
                new QueryWrapper<MesInterface>().eq("interface_code", code)
        );
        if (mesInterface == null) {
            throw new RuntimeException("未找到接口编号为 " + code + " 的MES接口配置信息！");
        }
        if (!"1".equals(mesInterface.getInterfaceStatus())) {
            throw new RuntimeException("接口 " + code + " 未启用！");
        }
        if (!"GET".equalsIgnoreCase(mesInterface.getRequestMethod())) {
            throw new RuntimeException("接口 " + code + " 的请求方式不是GET！");
        }
        return mesInterface;
    }

    /**
     * POST请求，给 JK013 使用
     */
    private JSONObject doPost(String url, String token, JSONObject requestBody) {
        return HttpClientUtil.doPostEnhanced(url, token, requestBody, baseCommonService, "MES生产订单", "syncProductOrderData");
    }

    /**
     * GET请求，给 JK020 使用
     */
    private JSONObject doGet(String url, String token, JSONObject requestParams) {
        return HttpClientUtil.doGetEnhanced(url, token, requestParams, baseCommonService, "MES用料清单", "syncMaterialDetails");
    }

    /**
     * 将JSON数据映射给WmsProductOrder实体
     */
    private void mapToProductOrder(WmsProductOrder order, JSONObject obj, Date now) {
        order.setBillNo(obj.getString("fbillno"));
        order.setBillDate(parseDate(obj.getString("fdate")));
        order.setProductOrganization(String.valueOf(obj.getLong("fprdorgid")));
        order.setBillStatus(obj.getString("fdocumentstatus"));
        order.setBusinessStatus(obj.getString("fstatus"));
        order.setWorkShop(obj.getString("fbijworkshopname"));
        order.setWorkGroup(obj.getString("fbijwkteamname"));
        order.setWorkGroupId(obj.getString("fbijwkteamid"));
        order.setWorkShopId(obj.getString("fbijworkshopid"));
        order.setWorkGroupcode(obj.getString("fbijwkteamnumber"));
        order.setItemCode(obj.getString("fmaterialnumber"));
        order.setItemName(obj.getString("fmaterialname"));
        order.setItemSpec(obj.getString("specificationname"));
        order.setItemUnit(String.valueOf(obj.getLong("funitid")));
        order.setQuantity(obj.getDouble("fqty"));
        order.setStartTime(parseDate(obj.getString("fplanstartdate")));
        order.setFinishTime(parseDate(obj.getString("fplanfinishdate")));
        order.setCheckBy(obj.getString("fapproveridfname"));
        order.setCheckTime(parseDate(obj.getString("fapprovedate")));

        // 业务属性
        order.setFromErp("1");
        order.setErpSync("0");
        order.setBusinessType("1");
        order.setUpdateTime(now);
        order.setFormalBill("0");
    }

    /**
     * 将字符串解析为Date，支持yyyy-MM-dd HH:mm:ss 和 yyyy-MM-dd
     */
    private Date parseDate(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        try {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(dateStr);
        } catch (Exception e) {
            try {
                return new SimpleDateFormat("yyyy-MM-dd").parse(dateStr);
            } catch (Exception ex) {
                return null;
            }
        }
    }

    /**
     * 获取本地IP地址
     */
    private String getLocalIpAddress() {
        try {
            InetAddress localHost = InetAddress.getLocalHost();
            return localHost.getHostAddress();
        } catch (UnknownHostException e) {
            return "未知";
        }
    }

    /**
     * 用于返回处理一页结果
     */
    private static class PageResult {
        int insertCount;
        int updateCount;

        // 新增：本页处理的订单记录，(orderId, billNo)
        List<SyncOrderRecord> processedOrders;

        public PageResult(int insertCount, int updateCount) {
            this.insertCount = insertCount;
            this.updateCount = updateCount;
        }

        public void setProcessedOrders(List<SyncOrderRecord> processedOrders) {
            this.processedOrders = processedOrders;
        }
        public List<SyncOrderRecord> getProcessedOrders() {
            return processedOrders;
        }
    }

    /**
     * 用于记录处理过的订单(主键ID, 订单号)
     */
    private static class SyncOrderRecord {
        private String orderId;
        private String billNo;

        public SyncOrderRecord(String orderId, String billNo) {
            this.orderId = orderId;
            this.billNo = billNo;
        }

        public String getOrderId() {
            return orderId;
        }
        public String getBillNo() {
            return billNo;
        }
    }

    @Override
    public void generateOfficialData(List<String> ids) {
        // 遍历每个生产订单ID
        ids.forEach(id -> {
            // 1. 通过主表ID查询对应主表的记录
            WmsProductOrder wmsProductOrder = wmsProductOrderMapper.selectById(id);
            if (wmsProductOrder == null) {
                throw new RuntimeException("未找到对应的生产订单，ID：" + id);
            }
            // 2. 根据主表ID查询用料清单明细
            List<WmsUseMaterialdetail> wmsUseMaterialdetails = wmsUseMaterialdetailMapper.selectByMainId(id);
            if (wmsUseMaterialdetails == null || wmsUseMaterialdetails.isEmpty()) {
                throw new RuntimeException("未找到对应的用料清单明细，主表ID：" + id);
            }

            // 3. 根据sourceDetail.getOrderNo()进行分组
            Map<String, List<WmsUseMaterialdetail>> groupMap = wmsUseMaterialdetails.stream()
                    .collect(Collectors.groupingBy(WmsUseMaterialdetail::getOrderNo));

            // 4. 针对每个分组生成单独的正式单据
            groupMap.forEach((orderNo, detailList) -> {
                // 4.1 组装主表数据
                WmsProrequist wmsProrequist = generateMainData(wmsProductOrder);

                // 4.2 组装明细表数据，只针对当前分组的数据
                List<WmsProrequistDetail> wmsProrequistDetails = generateDetailData(wmsProrequist, detailList);

                // 4.3 保存当前分组生成的正式单据
                wmsProrequistService.saveMain(wmsProrequist, wmsProrequistDetails);
                //4.4更新生产订单
                wmsProductOrder.setFormalBill("1");
                wmsProductOrderMapper.updateById(wmsProductOrder);
            });
        });
    }

    private WmsProrequist generateMainData(WmsProductOrder wmsProductOrder) {
        String code = (String) FillRuleUtil.executeRule(FillRuleConstant.PROREQUIST_ORDER, new JSONObject());
        WmsProrequist prorequist = new WmsProrequist();
        prorequist.setBillNo(code);
        // 2. 将生产订单原来的单据编号保存到workNo字段中（销售单号字段），便于追溯
        prorequist.setWorkNo(wmsProductOrder.getBillNo());
        prorequist.setBillStatus("0");

        // 3. 单据类型设置为固定值，如"正式单据"
        prorequist.setBillType("SCLL");

        // 4. 单据名称直接复制
        prorequist.setBillName(wmsProductOrder.getBillName());

        // 5. 单据状态更新为"正式"
        prorequist.setBillStatus("1");

        // 6. 复制ERP相关字段
        prorequist.setFromErp(wmsProductOrder.getFromErp());
        prorequist.setErpSync(wmsProductOrder.getErpSync());

        // 7. 将生产订单的单据日期作为正式数据的交付日期；若为空则使用当前日期
        prorequist.setDeliveryDate(wmsProductOrder.getBillDate() != null ? wmsProductOrder.getBillDate() : new Date());

        // 8. 复制生产组织及生产车间/班组相关字段
        prorequist.setProductOrganization(wmsProductOrder.getProductOrganization());
        prorequist.setWorkShop(wmsProductOrder.getWorkShop());
        prorequist.setWorkGroup(wmsProductOrder.getWorkGroup());
        prorequist.setWorkGroupcode(wmsProductOrder.getWorkGroupcode());

        // 9. 复制审核相关信息
        prorequist.setCheckBy(wmsProductOrder.getCheckBy());
        prorequist.setCheckTime(wmsProductOrder.getCheckTime());

        // 10. 复制备注信息
        prorequist.setRemark(wmsProductOrder.getRemark());
        prorequist.setRemark2(wmsProductOrder.getRemark());

        // 11. 更新创建和更新时间（正式数据生成时记录为当前时间）
        prorequist.setCreateTime(new Date());
        prorequist.setUpdateTime(new Date());
        prorequist.setItemCode(wmsProductOrder.getItemCode());
        prorequist.setItemName(wmsProductOrder.getItemName());
        prorequist.setItemUnit(wmsProductOrder.getItemUnit());
        prorequist.setQuantity(wmsProductOrder.getQuantity());

        return prorequist;
    }

    private List<WmsProrequistDetail> generateDetailData(WmsProrequist wmsProrequist,List<WmsUseMaterialdetail> wmsUseMaterialdetailList) {
        List<WmsProrequistDetail> detailList = new ArrayList<>();
        Date now = new Date();
        for (WmsUseMaterialdetail sourceDetail : wmsUseMaterialdetailList) {
            WmsProrequistDetail detail = new WmsProrequistDetail();
            // 设置序号：直接从用料明细复制
            detail.setSerialNumber(sourceDetail.getSerialNumber());
            detail.setBillNo(wmsProrequist.getBillNo());

            // 入库产品名称：可直接使用品名作为产品名称
            detail.setProductName(sourceDetail.getItemName());
            detail.setWorkNo(sourceDetail.getOrderNo());

            // 品号、品名直接复制
            detail.setItemCode(sourceDetail.getItemCode());
            detail.setItemName(sourceDetail.getItemName());

            // 批号、规格、单位、版本号：源数据中未提供，视实际情况可留空或后续补充
            detail.setBatchCode(null);
            detail.setItemSpec(null);
            detail.setItemUnit(null);
            detail.setVersion(null);

            // 数量：计划出库数量从应发数量复制；已出库数量直接使用已领数量
            detail.setPlanQty(sourceDetail.getPlanQty());
            // 已配货数量设为0（默认）
            detail.setDisQty(0.0);
            detail.setActQty(0.0);
            detail.setLineState("1");

            // 发货仓库从用料明细的仓库代码复制
            detail.setWarehouse(sourceDetail.getWarehouseCode());
            // 发货储位留空（若后续有其他逻辑可补充）
            detail.setLocateCode(null);

            // 备注直接复制
            detail.setRemark(sourceDetail.getRemark());

            // 设置创建和更新时间
            detail.setCreateTime(now);
            detail.setUpdateTime(now);
            detail.setFentryid(sourceDetail.getFentryid());

            // 添加到明细列表中
            detailList.add(detail);
        }
        return detailList;
    }

    /**
     * 查询根据物料编号查询物料信息
     */
    private WmsSpecMatchItem getWmsSpecMatchItemByItemCode(String itemCode) {
        QueryWrapper<WmsSpecMatchItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("item_code", itemCode);
        List<WmsSpecMatchItem> list = wmsSpecMatchItemService.list(queryWrapper);
        if (list != null && !list.isEmpty()) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public JSONObject getProductPlanStatus() {
        JSONObject result = new JSONObject();

        try {
            // 计算7天前的日期
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -7);
            Date sevenDaysAgo = calendar.getTime();

            // 使用优化的单次查询获取统计数据
            Map<String, Object> statsMap = wmsProductOrderMapper.getProductPlanStatusOptimized(sevenDaysAgo);

            // 从查询结果中获取统计数据，处理可能的null值
            int waitingPrepareCount = getIntValue(statsMap, "waitingPrepareCount");
            int preparingCount = getIntValue(statsMap, "preparingCount");
            int prepareCompleteCount = getIntValue(statsMap, "prepareCompleteCount");
            int exceptionOrdersCount = getIntValue(statsMap, "exceptionOrdersCount");

            // 计算完成率：备料完成数量 / (待备料数量 + 备料中数量 + 备料完成数量)
            int totalActiveOrders = waitingPrepareCount + preparingCount + prepareCompleteCount;
            double completionRate = 0.0;
            if (totalActiveOrders > 0) {
                completionRate = (double) prepareCompleteCount / totalActiveOrders * 100;
            }

            // 组装返回结果
            result.put("waitingPrepareCount", waitingPrepareCount);
            result.put("preparingCount", preparingCount);
            result.put("prepareCompleteCount", prepareCompleteCount);
            result.put("exceptionOrdersCount", exceptionOrdersCount);
            result.put("completionRate", Math.round(completionRate * 100.0) / 100.0); // 保留两位小数

            log.info("生产订单统计完成 - 待备料:{}, 备料中:{}, 备料完成:{}, 异常:{}, 完成率:{}%",
                    waitingPrepareCount, preparingCount, prepareCompleteCount,
                    exceptionOrdersCount, completionRate);

        } catch (Exception e) {
            log.error("查询生产订单统计数据异常", e);
            throw new RuntimeException("查询生产订单统计数据失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 安全地从Map中获取整数值，处理null和类型转换
     */
    private int getIntValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return 0;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            log.warn("无法转换值为整数: key={}, value={}", key, value);
            return 0;
        }
    }
}
