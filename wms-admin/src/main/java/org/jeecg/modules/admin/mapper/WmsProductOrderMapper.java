package org.jeecg.modules.admin.mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.admin.entity.WmsProductOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 生产订单
 * @Author: jeecg-boot
 * @Date:   2024-10-26
 * @Version: V1.0
 */
public interface WmsProductOrderMapper extends BaseMapper<WmsProductOrder> {

    /**
     * 查询生产订单统计数据（优化版本）
     * @param sevenDaysAgo 7天前的日期
     * @return 统计结果Map
     */
    Map<String, Object> getProductPlanStatusOptimized(@Param("sevenDaysAgo") Date sevenDaysAgo);

}
